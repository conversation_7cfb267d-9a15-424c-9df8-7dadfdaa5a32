# 📋 Luminara Paper优化集成计划

## 📝 1.1.0-PRE1 (基础框架与核心优化)

### 🏗️ 核心框架与库 (优先级：高)
- [ ] 构建系统更改 - 0002-Build-system-changes.patch
- [ ] 测试系统更改 - 0003-Test-changes.patch
- [ ] 添加FastUtil到Bukkit - 0004-Add-FastUtil-to-Bukkit.patch
- [ ] Adventure库集成 - 0005-Adventure.patch
- [ ] Paper工具类 - 0006-Paper-Utils.patch
- [ ] 使用ASM进行事件执行器 - 0007-Use-ASM-for-event-executors.patch
- [ ] Paper插件系统 - 0008-Paper-Plugins.patch
- [ ] 添加Position类 - 0009-Add-Position.patch
- [ ] Timings v2性能监控 - 0010-Timings-v2.patch

### ⚡ 关键性能优化 (优先级：高)
- [ ] 优化漏斗性能 - 0087-Optimize-Hoppers.patch / 0941-Optimize-Hoppers.patch
- [ ] 减少MetadataStoreBase中的线程同步 - 0034-Reduce-thread-synchronization-in-MetadataStoreBase.patch
- [ ] 权限系统性能并发改进 - 0149-Performance-Concurrency-Improvements-to-Permissions.patch
- [ ] 缓存Material.isBlock结果 - 0259-Cache-the-result-of-Material-isBlock.patch
- [ ] 使用Velocity压缩和加密原生库 - 0707-Use-Velocity-compression-and-cipher-natives.patch

### 🎮 基础API增强 (优先级：高)
- [ ] 命令行选项加载额外插件jar - 0011-Add-command-line-option-to-load-extra-plugin-jars-no.patch
- [ ] 添加getTPS方法 - 0013-Add-getTPS-method.patch
- [ ] 版本命令2.0 - 0014-Version-Command-2.0.patch
- [ ] 暴露服务器CommandMap - 0020-Expose-server-CommandMap.patch
- [ ] 添加异常报告事件 - 0022-Add-exception-reporting-event.patch

### 🔧 重要Bug修复 (优先级：高)
- [ ] 修复ServerListPingEvent标记为异步 - 0024-Fix-ServerListPingEvent-flagging-as-Async.patch
- [ ] 修复上游javadocs - 0055-Fix-upstream-javadocs.patch
- [ ] 修复Bukkit NamespacedKey恶作剧 - 0709-Fix-Bukkit-NamespacedKey-shenanigans.patch

## 📝 1.1.0-PRE2 (事件系统与API扩展)

### 🎯 核心事件系统 (优先级：高)
- [ ] 添加PlayerLocaleChangeEvent - 0016-Add-PlayerLocaleChangeEvent.patch
- [ ] 添加PlayerInitialSpawnEvent - 0019-Add-PlayerInitialSpawnEvent.patch
- [ ] 添加PlayerJumpEvent - 0071-Add-PlayerJumpEvent.patch
- [ ] 添加PlayerArmorChangeEvent - 0073-Add-PlayerArmorChangeEvent.patch
- [ ] AsyncTabCompleteEvent - 0075-AsyncTabCompleteEvent.patch
- [ ] 服务器Tick事件 - 0173-Server-Tick-Events.patch

### ⚡ 中级性能优化 (优先级：中)
- [ ] 优化间接乘客迭代 - 0655-Optimize-indirect-passenger-iteration.patch
- [ ] 优化通用POI访问 - 0683-Optimise-general-POI-access.patch
- [ ] 优化区块tick迭代 - 0685-Optimise-chunk-tick-iteration.patch
- [ ] 在tick中执行区块任务 - 0686-Execute-chunk-tasks-mid-tick.patch

### 🔧 实用工具API (优先级：中)
- [ ] 基本PlayerProfile API - 0059-Basic-PlayerProfile-API.patch
- [ ] 允许插件使用SLF4J进行日志记录 - 0069-Allow-plugins-to-use-SLF4J-for-logging.patch
- [ ] 获取BlockState而不创建快照的API - 0074-API-to-get-a-BlockState-without-a-snapshot.patch
- [ ] 暴露客户端协议版本和虚拟主机 - 0076-Expose-client-protocol-version-and-virtual-host.patch

## 📝 1.1.0-PRE3 (世界与实体系统)

### 🌍 世界相关API (优先级：中)
- [ ] 添加视距API - 0017-Add-view-distance-API.patch
- [ ] 实体来源API - 0015-Entity-Origin-API.patch
- [ ] 实体添加到/移除世界事件 - 0032-Entity-AddTo-RemoveFrom-World-Events.patch
- [ ] EntityPathfindEvent - 0033-EntityPathfindEvent.patch
- [ ] 异步区块API - 0142-Async-Chunks-API.patch
- [ ] isChunkGenerated API - 0140-isChunkGenerated-API.patch

### 🎮 实体相关API (优先级：中)
- [ ] 玩家影响生成API - 0012-Player-affects-spawning-API.patch
- [ ] 添加PlayerUseUnknownEntityEvent - 0036-Add-PlayerUseUnknownEntityEvent.patch
- [ ] 箭矢拾取规则API - 0038-Arrow-pickup-rule-API.patch
- [ ] EntityRegainHealthEvent isFastRegen API - 0039-EntityRegainHealthEvent-isFastRegen-API.patch
- [ ] 实体fromMobSpawner - 0062-Entity-fromMobSpawner.patch

### ⚡ 实体性能优化 (优先级：中)
- [ ] 优化附近玩家查找 - 0705-Optimise-nearby-player-lookups.patch
- [ ] 移除村民AI的streams - 0706-Remove-streams-for-villager-AI.patch
- [ ] 优化跟踪玩家的map实现 - 0701-Oprimise-map-impl-for-tracked-players.patch

## 📝 1.1.0-PRE4 (物品与库存系统)

### 🎨 物品系统核心 (优先级：中)
- [ ] LootTable API - 0040-LootTable-API.patch
- [ ] 被吃物品的自定义替换 - 0031-Custom-replacement-for-eaten-items.patch
- [ ] Item canEntityPickup - 0056-Item-canEntityPickup.patch
- [ ] PlayerAttemptPickupItemEvent - 0057-PlayerAttemptPickupItemEvent.patch
- [ ] PlayerPickupItemEvent setFlyAtPlayer - 0060-PlayerPickupItemEvent-setFlyAtPlayer.patch
- [ ] ItemStack getMaxItemUseDuration - 0107-ItemStack-getMaxItemUseDuration.patch
- [ ] ItemStack API添加数量标志说明 - 0115-ItemStack-API-additions-for-quantity-flags-lore.patch

### 📦 库存相关API (优先级：中)
- [ ] Inventory removeItemAnySlot - 0139-Inventory-removeItemAnySlot.patch
- [ ] InventoryCloseEvent原因API - 0119-InventoryCloseEvent-Reason-API.patch
- [ ] 库存关闭 - 0280-Inventory-close.patch
- [ ] Inventory getHolder方法不带方块快照 - 0199-Inventory-getHolder-method-without-block-snapshot.patch

### 🔧 物品相关事件 (优先级：中)
- [ ] PlayerPickupExperienceEvent - 0078-PlayerPickupExperienceEvent.patch
- [ ] ExperienceOrbMergeEvent - 0079-ExperienceOrbMergeEvent.patch
- [ ] 应用修补到XP API的能力 - 0080-Ability-to-apply-mending-to-XP-API.patch
- [ ] 添加PlayerItemCooldownEvent - 0225-Add-PlayerItemCooldownEvent.patch

## 📝 1.1.0-PRE5 (网络与通信系统)

### 🌐 网络优化 (优先级：高)
- [ ] 合并实体跟踪器数据包的flush调用 - 0696-Consolidate-flush-calls-for-entity-tracker-packets.patch
- [ ] 优化非flush数据包发送 - 0704-Optimise-non-flush-packet-sending.patch
- [ ] 为硬碰撞实体发送完整位置数据包 - 0699-Send-full-pos-packets-for-hard-colliding-entities.patch
- [ ] 添加数据包限制器配置 - 0694-Add-packet-limiter-config.patch

### 💬 聊天与通信 (优先级：中)
- [ ] 从spigot子类毕业bungeecord聊天API - 0021-Graduate-bungeecord-chat-API-from-spigot-subclasses.patch
- [ ] 添加BaseComponent sendMessage方法到CommandSender - 0023-Add-BaseComponent-sendMessage-methods-to-CommandSend.patch
- [ ] 玩家标签列表和标题API - 0025-Player-Tab-List-and-Title-APIs.patch
- [ ] 添加基于字符串的动作栏API - 0046-Add-String-based-Action-Bar-API.patch

### 🔗 握手与连接 (优先级：中)
- [ ] 添加握手事件允许插件处理客户端 - 0037-Add-handshake-event-to-allow-plugins-to-handle-clien.patch
- [ ] IllegalPacketEvent - 0048-IllegalPacketEvent.patch
- [ ] 添加PlayerConnectionCloseEvent - 0164-Add-PlayerConnectionCloseEvent.patch

## 📝 1.1.0-RELEASE (稳定性与完善)

### 🛠️ 配置与管理 (优先级：高)
- [ ] 添加配置选项防止玩家名称被 - 0054-Add-configuration-option-to-prevent-player-names-fro.patch
- [ ] 添加根管理员用户检测 - 0726-Add-root-admin-user-detection.patch
- [ ] 添加配置选项记录玩家IP地址 - 0724-Add-config-option-for-logging-player-ip-addresses.patch

### 🔧 关键Bug修复 (优先级：高)
- [ ] 修复商人库存在实体移除时不关闭 - 0710-Fix-merchant-inventory-not-closing-on-entity-removal.patch
- [ ] 确保有效载具状态 - 0714-Ensure-valid-vehicle-status.patch
- [ ] 防止软锁定的末地出口传送门生成 - 0715-Prevent-softlocked-end-exit-portal-generation.patch

### 📊 监控与统计 (优先级：中)
- [ ] 提供E/TE/Chunk计数统计方法 - 0051-Provide-E-TE-Chunk-count-stat-methods.patch
- [ ] 暴露内部当前tick - 0180-Expose-the-internal-current-tick.patch
- [ ] 添加tick时间API - 0186-Add-tick-times-API.patch
- [ ] 暴露MinecraftServer isRunning - 0187-Expose-MinecraftServer-isRunning.patch

## 📝 1.2.0-PRE1 (高级功能开发)

### 🚀 高级API功能 (优先级：中)
- [ ] Mob寻路API - 0147-Mob-Pathfinding-API.patch
- [ ] Mob目标API - 0195-Add-Mob-Goal-API.patch
- [ ] 添加村民声誉API - 0196-Add-villager-reputation-API.patch
- [ ] 生成原因API - 0197-Spawn-Reason-API.patch
- [ ] 添加更多进度API - 0318-Add-more-advancement-API.patch
- [ ] 添加暴击伤害API - 0319-Add-critical-damage-API.patch

### 🎨 高级物品系统 (优先级：中)
- [ ] 为CanPlaceOn和CanDestroy NBT值添加API - 0148-Add-an-API-for-CanPlaceOn-and-CanDestroy-NBT-values.patch
- [ ] 添加ItemStackRecipeChoice草案API - 0150-Add-ItemStackRecipeChoice-Draft-API.patch
- [ ] 实现熔炉烹饪速度倍数API - 0151-Implement-furnace-cook-speed-multiplier-API.patch
- [ ] Material API添加 - 0152-Material-API-additions.patch
- [ ] 添加Material标签 - 0153-Add-Material-Tags.patch

### ⚡ 高级性能优化 (优先级：中)
- [ ] 优化anyPlayerCloseEnoughForSpawning使用距离平方 - 0684-Optimize-anyPlayerCloseEnoughForSpawning-to-use-dist.patch
- [ ] 自定义blockstate状态查找表实现 - 0688-Custom-table-implementation-for-blockstate-state-loo.patch
- [ ] 手动内联BlockPosition中的方法 - 0690-Manually-inline-methods-in-BlockPosition.patch
- [ ] 确保内联getChunkAt具有加载逻辑 - 0693-Make-sure-inlined-getChunkAt-has-inlined-logic-for-l.patch

## 📝 1.2.0-PRE2 (世界生成与结构)

### 🌍 世界生成优化 (优先级：中)
- [ ] 为低核心数减少世界生成线程工作数 - 0708-Reduce-worldgen-thread-worker-count-for-low-core-cou.patch
- [ ] 可配置特征种子 - 0725-Configurable-feature-seeds.patch
- [ ] 添加缺失的结构集种子配置 - 0774-Add-missing-structure-set-seed-configs.patch
- [ ] 在自定义世界中有默认CustomSpawners的选项 - 0783-Option-to-have-default-CustomSpawners-in-custom-worl.patch

### 🏗️ 结构与生成API (优先级：中)
- [ ] 添加StructuresLocateEvent - 0246-Add-StructuresLocateEvent.patch
- [ ] 允许委托给原版区块生成 - 0326-Allow-delegation-to-vanilla-chunk-gen.patch
- [ ] 实现regenerateChunk - 0346-Implement-regenerateChunk.patch
- [ ] 添加getComputedBiome API - 0354-Add-getComputedBiome-API.patch

### 🔧 区块相关优化 (优先级：中)
- [ ] 光线追踪时不查找流体状态 - 0697-Don-t-lookup-fluid-state-when-raytracing.patch
- [ ] 不为AIR运行光线追踪逻辑 - 0700-Do-not-run-raytrace-logic-for-AIR.patch
- [ ] 优化BlockSoil附近水源查找 - 0702-Optimise-BlockSoil-nearby-water-lookup.patch
- [ ] 优化随机方块tick - 0703-Optimise-random-block-ticking.patch

## 📝 1.2.0-PRE3 (实体与AI系统)

### 🤖 AI与行为系统 (优先级：中)
- [ ] 为1.16 mob行为添加2个新TargetReasons - 0203-added-2-new-TargetReasons-for-1.16-mob-behavior.patch
- [ ] 添加LivingEntity getTargetEntity - 0155-Add-LivingEntity-getTargetEntity.patch
- [ ] 添加Mob lookAt API - 0286-Add-Mob-lookAt-API.patch
- [ ] 修复mob转换问题 - 0320-Fix-issues-with-mob-conversion.patch

### 🐾 实体行为API (优先级：中)
- [ ] 允许设置vex的召唤者 - 0120-Allow-setting-the-vex-s-summoner.patch
- [ ] 允许禁用盔甲架tick - 0126-Allow-disabling-armour-stand-ticking.patch
- [ ] 添加isCollidable方法到各个地方 - 0321-Add-isCollidable-methods-to-various-places.patch
- [ ] 山羊冲撞API - 0322-Goat-ram-API.patch

### ⚡ 实体性能优化 (优先级：中)
- [ ] 优化HashMapPalette - 0737-Optimize-HashMapPalette.patch
- [ ] 高度优化单个和多个AABB VoxelShapes - 0739-Highly-optimise-single-and-multi-AABB-VoxelShapes-an.patch
- [ ] 优化玩家移动数据包处理中的碰撞检查 - 0740-Optimise-collision-checking-in-player-move-packet-ha.patch
- [ ] 数组支持的同步实体数据 - 0982-Array-backed-synched-entity-data.patch

## 📝 1.2.0-RELEASE (功能完善与稳定)

### 🛠️ 高级配置 (优先级：中)
- [ ] 可配置物品框地图光标更新间隔 - 0656-Configurable-item-frame-map-cursor-update-interval.patch
- [ ] 可配置怪物生成的最大方块光照 - 0750-Configurable-max-block-light-for-monster-spawning.patch
- [ ] 使水生动物生成高度可配置 - 0757-Make-water-animal-spawn-height-configurable.patch
- [ ] 添加史莱姆生成的可配置高度 - 0763-Add-configurable-height-for-slime-spawn.patch

### 📊 高级监控 (优先级：中)
- [ ] 暴露游戏版本 - 0194-Expose-game-version.patch
- [ ] 添加Git信息到版本命令启动时 - 0146-Add-Git-information-to-version-command-on-startup.patch
- [ ] 暴露跟踪玩家 - 0258-Expose-Tracked-Players.patch
- [ ] 添加paper dumplisteners命令 - 0897-Add-paper-dumplisteners-command.patch

### 🔧 稳定性改进 (优先级：高)
- [ ] 为StructureTemplate.Pallete缓存使用CHM - 0772-Use-a-CHM-for-StructureTemplate.Pallete-cache.patch
- [ ] 公平执行世界区块任务 - 0779-Execute-chunk-tasks-fairly-for-worlds-while-waiting-.patch
- [ ] 优化玩家查找用于信标 - 0980-Optimize-player-lookups-for-beacons.patch
- [ ] 优化最近结构边界迭代 - 1024-Optimize-nearest-structure-border-iteration.patch

## 📝 未来版本规划 (1.3.0+)

### 🔮 待分类功能 (优先级：低)
> 以下功能将在后续版本中根据需求和稳定性进行分配

#### 🎯 大量事件系统
- 包含剩余的200+事件API
- 各种专门的游戏机制事件
- 高级玩家交互事件

#### 🔧 大量Bug修复
- 包含剩余的200+bug修复
- 兼容性改进
- 边缘情况处理

#### 🚀 高级功能
- 复杂的游戏机制API
- 高级性能优化
- 实验性功能

#### 🎨 完整组件系统
- 完整的物品组件支持
- 高级NBT操作
- 自定义数据处理

---

## ✅ 已完成

### 🎉 1.0.8系列
- [x] 1.0.8-PRE1：支持Velocity Modern转发（Port Mohist and PCF）
- [x] 1.0.8-PRE2：并入MPEM的部分优化项
- [x] 1.0.8-PRE2：支持Adventure库
- [x] 1.0.8-PRE3：使用Paper方法优化初始化世界的速度
- [x] 1.0.8-RELEASE：更多i18n（打算用AI，我很懒）

---

## 📋 版本开发指南

### 🎯 优先级说明
- **高优先级**：核心功能，必须在该版本完成
- **中优先级**：重要功能，建议在该版本完成
- **低优先级**：可选功能，可推迟到后续版本

### 📅 开发时间线建议
- **1.1.0-PRE1**: 2-3周 (基础框架)
- **1.1.0-PRE2**: 2-3周 (事件系统)
- **1.1.0-PRE3**: 2-3周 (世界实体)
- **1.1.0-PRE4**: 2-3周 (物品系统)
- **1.1.0-PRE5**: 2-3周 (网络通信)
- **1.1.0-RELEASE**: 1-2周 (稳定性)
- **1.2.0系列**: 每个PRE版本2-3周

### 🔄 迭代策略
1. 每个PRE版本专注特定领域
2. 优先实现高优先级功能
3. 充分测试后再进入下一版本
4. 保持向后兼容性
5. 及时修复发现的问题

### 📝 注意事项
- 某些patch可能有依赖关系，需要按顺序实现
- 性能优化patch需要特别测试
- 事件系统变更需要考虑插件兼容性
- 网络相关变更需要测试多种连接场景

## 📝 1.3.0-PRE1 (扩展事件系统)

### 🎯 更多事件API (优先级：中)
- [ ] ExperienceOrbs API用于原因/来源/触发玩家 - 0052-ExperienceOrbs-API-for-Reason-Source-Triggering-play.patch
- [ ] 添加UnknownCommandEvent - 0058-Add-UnknownCommandEvent.patch
- [ ] 肩膀实体释放API - 0061-Shoulder-Entities-Release-API.patch
- [ ] 配置文件查找事件 - 0063-Profile-Lookup-Events.patch
- [ ] ProfileWhitelistVerifyEvent - 0068-ProfileWhitelistVerifyEvent.patch
- [ ] PreCreatureSpawnEvent - 0081-PreCreatureSpawnEvent.patch
- [ ] PlayerNaturallySpawnCreaturesEvent - 0082-PlayerNaturallySpawnCreaturesEvent.patch
- [ ] PlayerAdvancementCriterionGrantEvent - 0085-PlayerAdvancementCriterionGrantEvent.patch
- [ ] EndermanEscapeEvent - 0096-EndermanEscapeEvent.patch
- [ ] EndermanAttackPlayerEvent - 0101-EndermanAttackPlayerEvent.patch
- [ ] WitchConsumePotionEvent - 0102-WitchConsumePotionEvent.patch
- [ ] WitchThrowPotionEvent - 0103-WitchThrowPotionEvent.patch
- [ ] WitchReadyPotionEvent - 0106-WitchReadyPotionEvent.patch
- [ ] 添加EntityTeleportEndGatewayEvent - 0108-Add-EntityTeleportEndGatewayEvent.patch
- [ ] PlayerReadyArrowEvent - 0112-PlayerReadyArrowEvent.patch
- [ ] 添加EntityKnockbackByEntityEvent和EntityPushedByEntityEvent - 0113-Add-EntityKnockbackByEntityEvent-and-EntityPushedByE.patch

### 🐉 实体相关事件 (优先级：中)
- [ ] EnderDragon事件 - 0122-EnderDragon-Events.patch
- [ ] PlayerElytraBoostEvent - 0123-PlayerElytraBoostEvent.patch
- [ ] PlayerLaunchProjectileEvent - 0124-PlayerLaunchProjectileEvent.patch
- [ ] EntityTransformedEvent - 0125-EntityTransformedEvent.patch
- [ ] AnvilDamageEvent - 0130-AnvilDamageEvent.patch
- [ ] 添加TNTPrimeEvent - 0131-Add-TNTPrimeEvent.patch
- [ ] Slime寻路事件 - 0136-Slime-Pathfinder-Events.patch
- [ ] 添加PhantomPreSpawnEvent - 0137-Add-PhantomPreSpawnEvent.patch
- [ ] PreSpawnerSpawnEvent - 0154-PreSpawnerSpawnEvent.patch
- [ ] 添加观察者目标事件 - 0158-Add-spectator-target-events.patch

## 📝 1.3.0-PRE2 (玩家交互事件)

### 👤 玩家事件扩展 (优先级：中)
- [ ] 支持EntityDismount/VehicleExit的取消抑制 - 0161-Support-cancellation-supression-of-EntityDismount-Ve.patch
- [ ] BlockDestroyEvent - 0167-BlockDestroyEvent.patch
- [ ] 添加WhitelistToggleEvent - 0168-Add-WhitelistToggleEvent.patch
- [ ] 添加GS4查询事件 - 0169-Add-GS4-Query-event.patch
- [ ] 添加PlayerPostRespawnEvent - 0170-Add-PlayerPostRespawnEvent.patch
- [ ] PlayerDeathEvent getItemsToKeep - 0174-PlayerDeathEvent-getItemsToKeep.patch
- [ ] PlayerDeathEvent shouldDropExperience - 0182-PlayerDeathEvent-shouldDropExperience.patch
- [ ] 添加ThrownEggHatchEvent - 0183-Add-ThrownEggHatchEvent.patch
- [ ] 实体跳跃API - 0184-Entity-Jump-API.patch
- [ ] 添加hand到BlockMultiPlaceEvent - 0185-add-hand-to-BlockMultiPlaceEvent.patch
- [ ] 添加PlayerAttackEntityCooldownResetEvent - 0190-Add-PlayerAttackEntityCooldownResetEvent.patch
- [ ] 添加和实现PlayerRecipeBookClickEvent - 0201-Add-and-implement-PlayerRecipeBookClickEvent.patch
- [ ] 添加PrepareResultEvent/PrepareGrindstoneEvent - 0205-Add-PrepareResultEvent-PrepareGrindstoneEvent.patch
- [ ] 添加BellRingEvent - 0207-Add-BellRingEvent.patch

### 🎮 游戏机制事件 (优先级：中)
- [ ] 添加EntityLoadCrossbowEvent - 0237-Add-EntityLoadCrossbowEvent.patch
- [ ] 添加WorldGameRuleChangeEvent - 0238-Added-WorldGameRuleChangeEvent.patch
- [ ] 添加ServerResourcesReloadedEvent - 0239-Added-ServerResourcesReloadedEvent.patch
- [ ] 添加BlockFailedDispenseEvent - 0240-Add-BlockFailedDispenseEvent.patch
- [ ] 添加PlayerLecternPageChangeEvent - 0241-Added-PlayerLecternPageChangeEvent.patch
- [ ] 添加PlayerLoomPatternSelectEvent - 0242-Added-PlayerLoomPatternSelectEvent.patch
- [ ] 添加BlockPreDispenseEvent - 0247-Add-BlockPreDispenseEvent.patch
- [ ] 添加PlayerChangeBeaconEffectEvent - 0249-Added-PlayerChangeBeaconEffectEvent.patch
- [ ] 添加PlayerStonecutterRecipeSelectEvent - 0250-Added-PlayerStonecutterRecipeSelectEvent.patch

## 📝 1.3.0-PRE3 (世界与环境事件)

### 🌍 世界事件系统 (优先级：中)
- [ ] 添加dropLeash变量到EntityUnleashEvent - 0251-Add-dropLeash-variable-to-EntityUnleashEvent.patch
- [ ] 添加DragonEggFormEvent - 0252-add-DragonEggFormEvent.patch
- [ ] EntityMoveEvent - 0253-EntityMoveEvent.patch
- [ ] 允许添加物品到BlockDropItemEvent - 0254-Allow-adding-items-to-BlockDropItemEvent.patch
- [ ] 添加世界边界事件 - 0260-Add-worldborder-events.patch
- [ ] 添加PlayerNameEntityEvent - 0261-added-PlayerNameEntityEvent.patch
- [ ] 添加配方到烹饪事件 - 0262-Add-recipe-to-cook-events.patch
- [ ] 添加PlayerDeepSleepEvent - 0270-Added-PlayerDeepSleepEvent.patch
- [ ] 添加PlayerBedFailEnterEvent - 0272-Added-PlayerBedFailEnterEvent.patch
- [ ] 引入信标激活/停用事件 - 0273-Introduce-beacon-activation-deactivation-events.patch
- [ ] PlayerMoveEvent改进 - 0274-PlayerMoveEvent-Improvements.patch
- [ ] 添加RespawnFlags到PlayerRespawnEvent - 0275-add-RespawnFlags-to-PlayerRespawnEvent.patch

### 🎨 高级事件功能 (优先级：中)
- [ ] 添加Adventure消息到PlayerAdvancementDoneEvent - 0278-Add-Adventure-message-to-PlayerAdvancementDoneEvent.patch
- [ ] 添加原始地址到AsyncPlayerPreLoginEvent - 0279-Add-raw-address-to-AsyncPlayerPreLoginEvent.patch
- [ ] 添加基本数据包API - 0282-Add-basic-Datapack-API.patch
- [ ] PlayerGameModeChangeEvent的添加 - 0283-additions-to-PlayerGameModeChangeEvent.patch
- [ ] 添加EntityInsideBlockEvent - 0288-Add-EntityInsideBlockEvent.patch
- [ ] 添加天气/雷暴变化事件的原因 - 0290-Add-cause-to-Weather-ThunderChangeEvents.patch
- [ ] 添加PlayerKickEvent原因 - 0292-Add-PlayerKickEvent-causes.patch

## 📝 1.3.0-RELEASE (事件系统完善)

### 🔔 特殊事件 (优先级：中)
- [ ] 添加PufferFishStateChangeEvent - 0293-Add-PufferFishStateChangeEvent.patch
- [ ] 添加BellRevealRaiderEvent - 0294-Add-BellRevealRaiderEvent.patch
- [ ] 添加ElderGuardianAppearanceEvent - 0295-Add-ElderGuardianAppearanceEvent.patch
- [ ] 添加WaterBottleSplashEvent - 0297-Add-WaterBottleSplashEvent.patch
- [ ] 添加PlayerArmSwingEvent - 0300-Adds-PlayerArmSwingEvent.patch
- [ ] 添加PlayerSignCommandPreprocessEvent - 0301-Add-PlayerSignCommandPreprocessEvent.patch
- [ ] 添加PlayerSetSpawnEvent - 0305-Add-PlayerSetSpawnEvent.patch
- [ ] 添加EntityDamageItemEvent - 0306-Added-EntityDamageItemEvent.patch
- [ ] 使EntityUnleashEvent可取消 - 0307-Make-EntityUnleashEvent-cancellable.patch
- [ ] 添加BlockBreakBlockEvent - 0309-Add-BlockBreakBlockEvent.patch
- [ ] 添加back EntityPortalExitEvent - 0314-add-back-EntityPortalExitEvent.patch

### 🎯 现代事件API (优先级：中)
- [ ] 添加PlayerItemFrameChangeEvent - 0325-Add-PlayerItemFrameChangeEvent.patch
- [ ] 移动VehicleCollisionEvent HandlerList向上 - 0328-Move-VehicleCollisionEvent-HandlerList-up.patch
- [ ] 多块更改API - 0340-Multi-Block-Change-API.patch
- [ ] 冻结Tick锁定API - 0342-Freeze-Tick-Lock-API.patch
- [ ] 添加TameableDeathMessageEvent - 0356-Add-TameableDeathMessageEvent.patch
- [ ] 添加EntityDyeEvent和CollarColorable接口 - 0361-Add-EntityDyeEvent-and-CollarColorable-interface.patch
- [ ] 添加PlayerStopUsingItemEvent - 0362-Add-PlayerStopUsingItemEvent.patch
- [ ] 添加WardenAngerChangeEvent - 0366-Add-WardenAngerChangeEvent.patch
- [ ] 添加EntityPortalReadyEvent - 0370-Add-EntityPortalReadyEvent.patch
- [ ] 添加PlayerInventorySlotChangeEvent - 0380-Add-PlayerInventorySlotChangeEvent.patch

## 📝 1.4.0-PRE1 (实用工具API扩展)

### 🔧 核心工具API (优先级：中)
- [ ] 改进马的鞍API - 0064-Improve-the-Saddle-API-for-Horses.patch
- [ ] 添加getI18NDisplayName API - 0065-Add-getI18NDisplayName-API.patch
- [ ] ensureServerConversions API - 0066-ensureServerConversions-API.patch
- [ ] LivingEntity setKiller - 0067-LivingEntity-setKiller.patch
- [ ] 处理实现日志配置中的插件前缀 - 0070-Handle-plugin-prefixes-in-implementation-logging-con.patch
- [ ] 添加插件修改父级的解决方案 - 0072-Add-workaround-for-plugins-modifying-the-parent-of-t.patch
- [ ] 在弃用的配方API上显示警告 - 0077-Display-warning-on-deprecated-recipe-API.patch
- [ ] 为骷髅头添加setPlayerProfile API - 0083-Add-setPlayerProfile-API-for-Skulls.patch
- [ ] 填充配置文件属性事件 - 0084-Fill-Profile-Property-Events.patch
- [ ] 添加盔甲架物品Meta - 0086-Add-ArmorStand-Item-Meta.patch

### 👤 玩家相关API (优先级：中)
- [ ] Tameable getOwnerUniqueId API - 0088-Tameable-getOwnerUniqueId-API.patch
- [ ] 在AsyncPreLoginEvent中更改PlayerProfile的能力 - 0089-Ability-to-change-PlayerProfile-in-AsyncPreLoginEven.patch
- [ ] 添加扩展的PaperServerListPingEvent - 0090-Add-extended-PaperServerListPingEvent.patch
- [ ] Player.setPlayerProfile API - 0091-Player.setPlayerProfile-API.patch
- [ ] getPlayerUniqueId API - 0092-getPlayerUniqueId-API.patch
- [ ] 添加传统ping支持到PaperServerListPingEvent - 0093-Add-legacy-ping-support-to-PaperServerListPingEvent.patch
- [ ] 添加openSign方法到HumanEntity - 0094-Add-openSign-method-to-HumanEntity.patch
- [ ] 添加封禁方法到Player对象 - 0095-Add-Ban-Methods-to-Player-Objects.patch
- [ ] Enderman.teleportRandomly - 0097-Enderman.teleportRandomly.patch

### 🌍 世界交互API (优先级：中)
- [ ] 额外的world.getNearbyEntities API - 0098-Additional-world.getNearbyEntities-API-s.patch
- [ ] Location.isChunkLoaded API - 0099-Location.isChunkLoaded-API.patch
- [ ] 扩展World.spawnParticle API并添加Builder - 0100-Expand-World.spawnParticle-API-and-add-Builder.patch
- [ ] Location.toBlockLocation/toCenterLocation - 0104-Location.toBlockLocation-toCenterLocation.patch
- [ ] PotionEffect克隆方法 - 0105-PotionEffect-clone-methods.patch

## 📝 1.4.0-PRE2 (物品与战斗系统)

### ⚔️ 战斗相关API (优先级：中)
- [ ] 使盾牌阻挡延迟可配置 - 0109-Make-shield-blocking-delay-configurable.patch
- [ ] EntityShootBowEvent consumeArrow和getArrowItem API - 0110-EntityShootBowEvent-consumeArrow-and-getArrowItem-AP.patch
- [ ] 添加getNearbyXXX方法到Location - 0111-Add-getNearbyXXX-methods-to-Location.patch
- [ ] 扩展爆炸API - 0114-Expand-Explosions-API.patch
- [ ] LivingEntity手部举起物品使用API - 0116-LivingEntity-Hand-Raised-Item-Use-API.patch
- [ ] RangedEntity API - 0117-RangedEntity-API.patch
- [ ] 添加World.getEntity UUID API - 0118-Add-World.getEntity-UUID-API.patch
- [ ] 暴露Player的攻击冷却方法 - 0144-Expose-attack-cooldown-methods-for-Player.patch
- [ ] 改进死亡事件 - 0145-Improve-death-events.patch

### 🏗️ 实体与结构API (优先级：中)
- [ ] 允许设置vex的召唤者 - 0120-Allow-setting-the-vex-s-summoner.patch
- [ ] 实体getChunk API - 0121-Entity-getChunk-API.patch
- [ ] 允许禁用盔甲架tick - 0126-Allow-disabling-armour-stand-ticking.patch
- [ ] SkeletonHorse添加 - 0127-SkeletonHorse-Additions.patch
- [ ] 扩展Location操作API - 0128-Expand-Location-Manipulation-API.patch
- [ ] 扩展ArmorStand API - 0129-Expand-ArmorStand-API.patch
- [ ] 提供区块坐标作为Long API - 0132-Provide-Chunk-Coordinates-as-a-Long-API.patch
- [ ] 从区块获取Tile实体而不创建快照的能力 - 0133-Ability-to-get-Tile-Entities-from-a-chunk-without-sn.patch
- [ ] 不为Timings Tile实体报告使用快照 - 0134-Don-t-use-snapshots-for-Timings-Tile-Entity-reports.patch
- [ ] 允许通过long键访问方块 - 0135-Allow-Blocks-to-be-accessed-via-a-long-key.patch

### 🎯 生物AI与行为 (优先级：中)
- [ ] 添加更多Creeper API - 0138-Add-More-Creeper-API.patch
- [ ] 添加源方块构造器和getChangedBlockData - 0141-Add-source-block-constructor-and-getChangedBlockData.patch
- [ ] 添加光线追踪方法到LivingEntity - 0143-Add-ray-tracing-methods-to-LivingEntity.patch

## 📝 1.4.0-PRE3 (高级材料与物品系统)

### 🎨 材料系统扩展 (优先级：中)
- [ ] 添加LivingEntity getTargetEntity - 0155-Add-LivingEntity-getTargetEntity.patch
- [ ] 添加太阳相关API - 0156-Add-sun-related-API.patch
- [ ] 海龟API - 0157-Turtle-API.patch
- [ ] 添加更多女巫API - 0159-Add-more-Witch-API.patch
- [ ] 使默认权限消息可配置 - 0160-Make-the-default-permission-message-configurable.patch
- [ ] 添加更多僵尸API - 0162-Add-more-Zombie-API.patch
- [ ] 更改保留频道检查为合理 - 0163-Change-the-reserved-channel-check-to-be-sensible.patch
- [ ] 添加API替换OfflinePlayer getLastPlayed - 0165-Add-APIs-to-replace-OfflinePlayer-getLastPlayed.patch
- [ ] 添加ItemStack配方API助手方法 - 0166-Add-ItemStack-Recipe-API-helper-methods.patch

### 📊 监控与统计扩展 (优先级：中)
- [ ] 添加高度图API - 0175-Add-Heightmap-API.patch
- [ ] Mob生成器API增强 - 0176-Mob-Spawner-API-Enhancements.patch
- [ ] 添加BlockSoundGroup接口 - 0177-Add-BlockSoundGroup-interface.patch
- [ ] 修改PlayerInteractAtEntityEvent javadoc为ArmorStand - 0178-Amend-PlayerInteractAtEntityEvent-javadoc-for-ArmorS.patch
- [ ] 增加自定义载荷频道消息大小 - 0179-Increase-custom-payload-channel-message-size.patch
- [ ] 改进Block breakNaturally API - 0181-Improve-Block-breakNaturally-API.patch
- [ ] 添加原始字节ItemStack序列化 - 0188-Add-Raw-Byte-ItemStack-Serialization.patch
- [ ] 添加Player客户端选项API - 0189-Add-Player-Client-Options-API.patch

### 🔧 修复与改进 (优先级：中)
- [ ] 修复Potion toItemStack交换扩展和升级 - 0191-Fix-Potion-toItemStack-swapping-the-extended-and-upg.patch
- [ ] 添加物品槽便利方法 - 0192-Add-item-slot-convenience-methods.patch
- [ ] 潜在床API - 0198-Potential-bed-API.patch

## 📝 1.4.0-RELEASE (组件与物品系统完善)

### 🎨 完整组件系统 (优先级：中)
- [ ] 在ItemMeta中支持组件 - 0202-Support-components-in-ItemMeta.patch
- [ ] 添加实体液体API - 0204-Add-entity-liquid-API.patch
- [ ] 品牌支持 - 0206-Brand-support.patch
- [ ] 添加月相API - 0208-Add-moon-phase-API.patch
- [ ] 添加playPickupItemAnimation到LivingEntity - 0209-Add-playPickupItemAnimation-to-LivingEntity.patch
- [ ] 添加更多Evoker API - 0210-Add-more-Evoker-API.patch
- [ ] 添加获取翻译键的方法 - 0211-Add-methods-to-get-translation-keys.patch
- [ ] 从ItemStack/Entity创建HoverEvent - 0212-Create-HoverEvent-from-ItemStack-Entity.patch
- [ ] 添加额外的开放容器api到HumanEntity - 0213-Add-additional-open-container-api-to-HumanEntity.patch

### 🔢 实体与数据管理 (优先级：中)
- [ ] 暴露实体计数器允许插件使用有效ID - 0214-Expose-the-Entity-Counter-to-allow-plugins-to-use-va.patch
- [ ] 实体isTicking - 0215-Entity-isTicking.patch
- [ ] 村民resetOffers - 0216-Villager-resetOffers.patch
- [ ] Player鞘翅推进API - 0217-Player-elytra-boost-API.patch
- [ ] 添加getOfflinePlayerIfCached String - 0218-Add-getOfflinePlayerIfCached-String.patch
- [ ] 添加忽略折扣API - 0219-Add-ignore-discounts-API.patch
- [ ] 物品无年龄无玩家拾取 - 0220-Item-no-age-no-player-pickup.patch
- [ ] 信标API自定义效果范围 - 0221-Beacon-API-custom-effect-ranges.patch

### 🎯 玩家交互增强 (优先级：中)
- [ ] 添加退出原因API - 0222-Add-API-for-quit-reason.patch
- [ ] 添加破坏速度API - 0223-Add-Destroy-Speed-API.patch
- [ ] 添加LivingEntity clearActiveItem - 0224-Add-LivingEntity-clearActiveItem.patch
- [ ] 更多闪电API - 0226-More-lightning-API.patch
- [ ] 添加PlayerShearBlockEvent - 0227-Add-PlayerShearBlockEvent.patch
- [ ] Player区块加载卸载事件 - 0228-Player-Chunk-Load-Unload-Events.patch
- [ ] 暴露LivingEntity受伤方向 - 0229-Expose-LivingEntity-hurt-direction.patch

## 📝 1.5.0-PRE1 (高级游戏机制)

### 🛏️ 生活机制API (优先级：中)
- [ ] 添加OBSTRUCTED原因到BedEnterResult - 0230-Add-OBSTRUCTED-reason-to-BedEnterResult.patch
- [ ] 添加PlayerTradeEvent - 0231-Added-PlayerTradeEvent.patch
- [ ] 添加TargetHitEvent API - 0232-Add-TargetHitEvent-API.patch
- [ ] 额外的方块Material API - 0233-Additional-Block-Material-API-s.patch
- [ ] 添加API从船和矿车获取Material - 0234-Add-API-to-get-Material-from-Boats-and-Minecarts.patch
- [ ] 添加PlayerFlowerPotManipulateEvent - 0235-Add-PlayerFlowerPotManipulateEvent.patch
- [ ] 僵尸API破门 - 0236-Zombie-API-breaking-doors.patch
- [ ] 添加缺失的原版标签 - 0248-Added-missing-vanilla-tags.patch

### 🔧 系统工具增强 (优先级：中)
- [ ] 添加getMainThreadExecutor到BukkitScheduler - 0255-Add-getMainThreadExecutor-to-BukkitScheduler.patch
- [ ] living entity允许属性注册 - 0256-living-entity-allow-attribute-registration.patch
- [ ] 添加缺失效果 - 0257-Add-missing-effects.patch
- [ ] 添加Block isValidTool - 0263-Add-Block-isValidTool.patch
- [ ] 扩展世界键API - 0264-Expand-world-key-API.patch
- [ ] 物品稀有度API - 0265-Item-Rarity-API.patch
- [ ] 暴露协议版本 - 0266-Expose-protocol-version.patch
- [ ] 添加isDeeplySleeping到HumanEntity - 0267-add-isDeeplySleeping-to-HumanEntity.patch

### 🔥 熔炉与燃料系统 (优先级：中)
- [ ] 添加consumeFuel到FurnaceBurnEvent - 0268-add-consumeFuel-to-FurnaceBurnEvent.patch
- [ ] 添加get/set掉落几率到EntityEquipment - 0269-add-get-set-drop-chance-to-EntityEquipment.patch
- [ ] 更多世界API - 0271-More-World-API.patch
- [ ] 添加更多WanderingTrader API - 0276-Add-more-WanderingTrader-API.patch
- [ ] 添加EntityBlockStorage clearEntities - 0277-Add-EntityBlockStorage-clearEntities.patch

## 📝 1.5.0-PRE2 (特殊生物与效果)

### 🌞 生物特性API (优先级：中)
- [ ] 添加应该在阳光下燃烧API给幻翼和骷髅 - 0281-Add-a-should-burn-in-sunlight-API-for-Phantoms-and-S.patch
- [ ] ItemStack修复检查API - 0284-ItemStack-repair-check-API.patch
- [ ] 更多附魔API - 0285-More-Enchantment-API.patch
- [ ] ItemStack editMeta - 0287-ItemStack-editMeta.patch
- [ ] 物品默认属性API - 0289-Attributes-API-for-item-defaults.patch
- [ ] 更多有盖方块API - 0291-More-Lidded-Block-API.patch
- [ ] 缺失实体API - 0299-Missing-Entity-API.patch
- [ ] Stinger API - 0303-Stinger-API.patch
- [ ] 重写LogEvents以在堆栈跟踪中包含源jar - 0304-Rewrite-LogEvents-to-contain-the-source-jars-in-stac.patch

### 🎯 目标与交互 (优先级：中)
- [ ] 更改EnderEye目标而不更改其他东西 - 0308-Change-EnderEye-target-without-changing-other-things.patch
- [ ] 添加Action的左右点击助手 - 0310-Add-helpers-for-left-right-click-to-Action.patch
- [ ] 铁匠配方中防止NBT复制的选项 - 0311-Option-to-prevent-NBT-copy-in-smithing-recipes.patch
- [ ] 更多CommandBlock API - 0312-More-CommandBlock-API.patch
- [ ] 添加缺失的团队侧边栏显示槽 - 0313-Add-missing-team-sidebar-display-slots.patch
- [ ] 添加寻找闪电打击目标的方法 - 0315-Add-methods-to-find-targets-for-lightning-strikes.patch
- [ ] 获取实体默认属性 - 0316-Get-entity-default-attributes.patch
- [ ] 左撇子API - 0317-Left-handed-API.patch

### ⚔️ 战斗与伤害系统 (优先级：中)
- [ ] 修复mob转换问题 - 0320-Fix-issues-with-mob-conversion.patch
- [ ] 添加isCollidable方法到各个地方 - 0321-Add-isCollidable-methods-to-various-places.patch
- [ ] 山羊冲撞API - 0322-Goat-ram-API.patch
- [ ] 添加重置单个分数的API - 0323-Add-API-for-resetting-a-single-score.patch
- [ ] 添加原始字节实体序列化 - 0324-Add-Raw-Byte-Entity-Serialization.patch

## 📝 1.5.0-PRE3 (营火与环境系统)

### 🔥 营火与环境 (优先级：中)
- [ ] 添加更多营火API - 0327-Add-more-Campfire-API.patch
- [ ] 改进记分板条目 - 0329-Improve-scoreboard-entries.patch
- [ ] 实体粉雪API - 0330-Entity-powdered-snow-API.patch
- [ ] 添加物品实体健康API - 0331-Add-API-for-item-entity-health.patch
- [ ] 暴露isFuel和canSmelt方法到FurnaceInventory - 0332-Expose-isFuel-and-canSmelt-methods-to-FurnaceInvento.patch
- [ ] Bucketable API - 0333-Bucketable-API.patch
- [ ] 默认配置注释解析的系统属性 - 0334-System-prop-for-default-config-comment-parsing.patch
- [ ] 从WorldInfo暴露原版BiomeProvider - 0335-Expose-vanilla-BiomeProvider-from-WorldInfo.patch

### 📊 数据容器与记分板 (优先级：中)
- [ ] 添加新重载到PersistentDataContainer has - 0336-Add-new-overload-to-PersistentDataContainer-has.patch
- [ ] 记分板的多个条目 - 0337-Multiple-Entries-with-Scoreboards.patch
- [ ] 添加getHostname到AsyncPlayerPreLoginEvent - 0338-Added-getHostname-to-AsyncPlayerPreLoginEvent.patch
- [ ] 在奇怪的EventHandler返回类型上警告 - 0339-Warn-on-strange-EventHandler-return-types.patch
- [ ] 修复NotePlayEvent - 0341-Fix-NotePlayEvent.patch
- [ ] 海豚API - 0343-Dolphin-API.patch
- [ ] 更多PotionEffectType API - 0344-More-PotionEffectType-API.patch

### 🎮 高级游戏功能 (优先级：中)
- [ ] 创建转发反馈的命令发送者API - 0345-API-for-creating-command-sender-which-forwards-feedb.patch
- [ ] 添加GameEvent标签 - 0347-Add-GameEvent-tags.patch
- [ ] 熔炉RecipesUsed API - 0348-Furnace-RecipesUsed-API.patch
- [ ] 可配置的雕塑传感器监听器范围 - 0349-Configurable-sculk-sensor-listener-range.patch
- [ ] 添加缺失的方块数据最小值和最大值 - 0350-Add-missing-block-data-mins-and-maxes.patch

## 📝 1.5.0-RELEASE (药水与投射物系统)

### 🧪 药水与附魔系统 (优先级：中)
- [ ] 自定义药水混合 - 0351-Custom-Potion-Mixes.patch
- [ ] 暴露熔炉矿车推力值 - 0352-Expose-furnace-minecart-push-values.patch
- [ ] 更多投射物API - 0353-More-Projectile-API.patch
- [ ] 添加enchantWithLevels API - 0355-Add-enchantWithLevels-API.patch
- [ ] 允许更改末影龙的讲台 - 0357-Allow-to-change-the-podium-of-the-EnderDragon.patch
- [ ] 修复EntityBreakDoorEvent中不正确的新blockstate - 0358-Fix-incorrect-new-blockstate-in-EntityBreakDoorEvent.patch
- [ ] 添加pre-unbreaking数量到PlayerItemDamageEvent - 0359-Add-pre-unbreaking-amount-to-PlayerItemDamageEvent.patch

### 🌍 世界创建与管理 (优先级：中)
- [ ] WorldCreator keepSpawnLoaded - 0360-WorldCreator-keepSpawnLoaded.patch
- [ ] FallingBlock自动过期设置 - 0363-FallingBlock-auto-expire-setting.patch
- [ ] 键控猫类型 - 0364-Keyed-Cat-Type.patch
- [ ] 添加方法isTickingWorlds到Bukkit - 0365-Add-method-isTickingWorlds-to-Bukkit.patch
- [ ] 可命名横幅API - 0367-Nameable-Banner-API.patch
- [ ] 添加Player getFishHook - 0368-Add-Player-getFishHook.patch
- [ ] 更多传送API - 0369-More-Teleport-API.patch

### 💬 聊天与通信增强 (优先级：中)
- [ ] 自定义聊天完成建议API - 0371-Custom-Chat-Completion-Suggestions-API.patch
- [ ] 碰撞API - 0372-Collision-API.patch
- [ ] 方块Ticking API - 0373-Block-Ticking-API.patch
- [ ] 添加NamespacedKey生物群系方法 - 0374-Add-NamespacedKey-biome-methods.patch
- [ ] 也从LibraryLoader加载资源 - 0375-Also-load-resources-from-LibraryLoader.patch
- [ ] 为PersistentDataContainer添加字节数组序列化/反序列化 - 0376-Added-byte-array-serialization-deserialization-for-P.patch
- [ ] 添加消费者参数到ProjectileSource launchProjectile - 0377-Add-a-consumer-parameter-to-ProjectileSource-launchP.patch
- [ ] 在YamlConfigOptions中暴露代码点限制并增加 - 0378-Expose-codepoint-limit-in-YamlConfigOptions-and-incr.patch
- [ ] 添加getDrops到BlockState - 0379-Add-getDrops-to-BlockState.patch
- [ ] 长者守卫者外观API - 0381-Elder-Guardian-appearance-API.patch
- [ ] 允许更改床的占用属性 - 0382-Allow-changing-bed-s-occupied-property.patch

## 📝 1.6.0-PRE1 (大量Bug修复第一阶段)

### 🔧 核心修复 (优先级：高)
- [ ] 修复商人库存在实体移除时不关闭 - 0710-Fix-merchant-inventory-not-closing-on-entity-removal.patch
- [ ] 在建议根节点前检查要求 - 0711-Check-requirement-before-suggesting-root-nodes.patch
- [ ] 不响应ServerboundCommandSuggestionPacket - 0712-Don-t-respond-to-ServerboundCommandSuggestionPacket-.patch
- [ ] 修复热带鱼桶meta上的setPatternColor - 0713-Fix-setPatternColor-on-tropical-fish-bucket-meta.patch
- [ ] 修复CocaoDecorator在尝试获取时导致崩溃 - 0716-Fix-CocaoDecorator-causing-a-crash-when-trying-to-ge.patch
- [ ] 不记录调试日志被禁用 - 0717-Don-t-log-debug-logging-being-disabled.patch
- [ ] 修复空级别访问的各种菜单 - 0718-fix-various-menus-with-empty-level-accesses.patch
- [ ] 保留过度堆叠的战利品 - 0719-Preserve-overstacked-loot.patch
- [ ] 在缺失位置更新头部旋转 - 0720-Update-head-rotation-in-missing-places.patch
- [ ] 防止意外的光方块操作 - 0721-prevent-unintended-light-block-manipulation.patch

### 🎯 数据与状态修复 (优先级：高)
- [ ] 修复CraftCriteria默认映射 - 0722-Fix-CraftCriteria-defaults-map.patch
- [ ] 修复上游的方块状态工厂 - 0723-Fix-upstreams-block-state-factories.patch
- [ ] 总是允许在火球中更改物品 - 0727-Always-allow-item-changing-in-Fireball.patch
- [ ] 不尝试传送死亡实体 - 0728-don-t-attempt-to-teleport-dead-entities.patch
- [ ] 防止通过重复暴击产生过度速度 - 0729-Prevent-excessive-velocity-through-repeated-crits.patch
- [ ] 移除使用弃用移除的客户端代码 - 0730-Remove-client-side-code-using-deprecated-for-removal.patch
- [ ] 修复从RecipeIterator移除配方 - 0731-Fix-removing-recipes-from-RecipeIterator.patch
- [ ] 防止在装备和其他地方发送过大物品数据 - 0732-Prevent-sending-oversized-item-data-in-equipment-and.patch
- [ ] 从客户端隐藏不必要的itemmeta - 0733-Hide-unnecessary-itemmeta-from-clients.patch
- [ ] 修复Spigot生长修饰符 - 0734-Fix-Spigot-growth-modifiers.patch

## 📝 1.6.0-PRE2 (大量Bug修复第二阶段)

### 🔧 容器与库存修复 (优先级：高)
- [ ] 防止ContainerOpenersCounter openCount变为负数 - 0735-Prevent-ContainerOpenersCounter-openCount-from-going.patch
- [ ] 只有在序列化时才将区块数据写入磁盘 - 0743-Only-write-chunk-data-to-disk-if-it-serializes-witho.patch
- [ ] 修复绊线状态不一致 - 0744-Fix-tripwire-state-inconsistency.patch
- [ ] 在传送命令中转发CraftEntity - 0745-Forward-CraftEntity-in-teleport-command.patch
- [ ] 修复实体类型标签在选择器中的建议 - 0749-Fix-entity-type-tags-suggestions-in-selectors.patch
- [ ] 修复粘性活塞和BlockPistonRetractEvent - 0751-Fix-sticky-pistons-and-BlockPistonRetractEvent.patch
- [ ] 正确加载大于127的效果放大器 - 0752-Load-effect-amplifiers-greater-than-127-correctly.patch
- [ ] 修复蜜蜂在蜂巢内老化 - 0754-Fix-bees-aging-inside-hives.patch
- [ ] 验证用户名 - 0756-Validate-usernames.patch
- [ ] 在异常时重置放置的方块 - 0762-Reset-placed-block-on-exception.patch

### 🎮 游戏机制修复 (优先级：高)
- [ ] 修复幼体僵尸的经验奖励 - 0765-Fix-xp-reward-for-baby-zombies.patch
- [ ] 为非法聊天在主线程踢出 - 0766-Kick-on-main-for-illegal-chat.patch
- [ ] 修复取消的粉雪桶放置 - 0776-Fix-cancelled-powdered-snow-bucket-placement.patch
- [ ] 添加缺失的Validate调用到CraftServer getSpawnLimit - 0777-Add-missing-Validate-calls-to-CraftServer-getSpawnLi.patch
- [ ] 修复实体位置不同步 - 0785-Fix-Entity-Position-Desync.patch
- [ ] 强制关闭世界加载屏幕 - 0787-Force-close-world-loading-screen.patch
- [ ] 修复掉落方块生成方法 - 0788-Fix-falling-block-spawn-methods.patch
- [ ] 修复取消穿透箭的ProjectileHitEvent - 0790-Fix-cancelling-ProjectileHitEvent-for-piercing-arrow.patch
- [ ] 修复沼泽小屋猫生成死锁 - 0792-Fix-swamp-hut-cat-generation-deadlock.patch
- [ ] 传送时不允许玩家的载具移动 - 0793-Don-t-allow-vehicle-movement-from-players-while-tele.patch

## 📝 1.6.0-PRE3 (大量Bug修复第三阶段)

### 🔧 物品与实体修复 (优先级：高)
- [ ] 使一些itemstacks非空 - 0795-Make-some-itemstacks-nonnull.patch
- [ ] 修复在unloadWorld中的保存 - 0797-Fix-saving-in-unloadWorld.patch
- [ ] 缓冲OOB setBlock调用 - 0798-Buffer-OOB-setBlock-calls.patch
- [ ] 修复EntityChangeBlockEvent的新方块数据 - 0800-Fix-new-block-data-for-EntityChangeBlockEvent.patch
- [ ] 修复玩家战利品表在mob战利品游戏规则时运行 - 0801-fix-player-loottables-running-when-mob-loot-gamerule.patch
- [ ] 确保实体乘客世界匹配被骑实体 - 0802-Ensure-entity-passenger-world-matches-ridden-entity.patch
- [ ] 防范无效实体位置 - 0803-Guard-against-invalid-entity-positions.patch
- [ ] 缓存资源键 - 0804-cache-resource-keys.patch
- [ ] 修复世界生成期间NBT片段覆盖方块实体 - 0806-Fix-NBT-pieces-overriding-a-block-entity-during-worl.patch
- [ ] 修复RED_MUSHROOM的StructureGrowEvent种类 - 0807-Fix-StructureGrowEvent-species-for-RED_MUSHROOM.patch

### 🌍 世界与区块修复 (优先级：高)
- [ ] 防止tile实体副本加载区块 - 0808-Prevent-tile-entity-copies-loading-chunks.patch
- [ ] 在PlayerList获取中使用用户名而不是显示名称 - 0809-Use-username-instead-of-display-name-in-PlayerList-g.patch
- [ ] 修复史莱姆生成器不在史莱姆区块外生成 - 0810-Fix-slime-spawners-not-spawning-outside-slime-chunks.patch
- [ ] 为游戏规则回调传递ServerLevel - 0811-Pass-ServerLevel-for-gamerule-callbacks.patch
- [ ] 修复CraftPersistentDataTypeRegistry中的CME - 0814-Fix-CME-in-CraftPersistentDataTypeRegistry.patch
- [ ] 在正确位置触发bee_nest_destroyed触发器 - 0815-Trigger-bee_nest_destroyed-trigger-in-the-correct-pl.patch
- [ ] 在初始填充时触发CauldronLevelChange - 0817-Fire-CauldronLevelChange-on-initial-fill.patch
- [ ] 修复粉雪锅炉不变成水 - 0818-fix-powder-snow-cauldrons-not-turning-to-water.patch
- [ ] 不tick标记 - 0821-Don-t-tick-markers.patch
- [ ] 不接受无效客户端设置 - 0822-Do-not-accept-invalid-client-settings.patch

## 📝 1.6.0-RELEASE (网络与安全修复)

### 🔐 网络与安全 (优先级：高)
- [ ] 添加代理协议支持 - 0823-Add-support-for-Proxy-Protocol.patch
- [ ] 修复OfflinePlayer getBedSpawnLocation - 0824-Fix-OfflinePlayer-getBedSpawnLocation.patch
- [ ] 修复烟熏炉和高炉的FurnaceInventory - 0825-Fix-FurnaceInventory-for-smokers-and-blast-furnaces.patch
- [ ] 清理发送的BlockEntity NBT - 0826-Sanitize-Sent-BlockEntity-NBT.patch
- [ ] 默认禁用书籍中的组件选择器解析 - 0827-Disable-component-selector-resolving-in-books-by-def.patch
- [ ] 防止实体加载导致异步查找 - 0828-Prevent-entity-loading-causing-async-lookups.patch
- [ ] 在被tick时抛出世界创建异常 - 0829-Throw-exception-on-world-create-while-being-ticked.patch
- [ ] 添加Velocity IP转发支持 - 0856-Add-Velocity-IP-Forwarding-Support.patch
- [ ] 在ServerLoginPacketListenerImpl中使用线程安全随机 - 0857-Use-thread-safe-random-in-ServerLoginPacketListenerI.patch

### 🔧 系统稳定性 (优先级：高)
- [ ] 添加交流电红石实现 - 0830-Add-Alternate-Current-redstone-implementation.patch
- [ ] 不在艺术更新时重新发送实体 - 0831-Dont-resent-entity-on-art-update.patch
- [ ] 添加严格进度维度检查选项 - 0833-Add-option-for-strict-advancement-dimension-checks.patch
- [ ] 添加缺失的重要BlockStateListPopulator方法 - 0834-Add-missing-important-BlockStateListPopulator-method.patch
- [ ] 不向命令方块广播消息 - 0836-Don-t-broadcast-messages-to-command-blocks.patch
- [ ] 防止空物品被添加到世界 - 0837-Prevent-empty-items-from-being-added-to-world.patch

## 📝 1.7.0+ (未来版本 - 剩余功能)

### 🔮 待分类的大量功能 (优先级：低)

#### 🎯 剩余性能优化
- [ ] 优化anyPlayerCloseEnoughForSpawning使用距离平方 - 0684-Optimize-anyPlayerCloseEnoughForSpawning-to-use-dist.patch
- [ ] 自定义blockstate状态查找表实现 - 0688-Custom-table-implementation-for-blockstate-state-loo.patch
- [ ] 手动内联BlockPosition中的方法 - 0690-Manually-inline-methods-in-BlockPosition.patch
- [ ] 确保内联getChunkAt具有加载逻辑 - 0693-Make-sure-inlined-getChunkAt-has-inlined-logic-for-l.patch
- [ ] 光线追踪时不查找流体状态 - 0697-Don-t-lookup-fluid-state-when-raytracing.patch
- [ ] 不为AIR运行光线追踪逻辑 - 0700-Do-not-run-raytrace-logic-for-AIR.patch
- [ ] 优化BlockSoil附近水源查找 - 0702-Optimise-BlockSoil-nearby-water-lookup.patch
- [ ] 优化随机方块tick - 0703-Optimise-random-block-ticking.patch
- [ ] 优化HashMapPalette - 0737-Optimize-HashMapPalette.patch
- [ ] 高度优化单个和多个AABB VoxelShapes - 0739-Highly-optimise-single-and-multi-AABB-VoxelShapes-an.patch
- [ ] 优化玩家移动数据包处理中的碰撞检查 - 0740-Optimise-collision-checking-in-player-move-packet-ha.patch

#### 🔧 剩余Bug修复 (200+项)
- [ ] 修复SplashPotion和LingeringPotion生成的CCE - 0838-Fix-CCE-for-SplashPotion-and-LingeringPotion-spawnin.patch
- [ ] 不在资源包拒绝消息中打印组件 - 0839-Don-t-print-component-in-resource-pack-rejection-mes.patch
- [ ] 不为动态游戏事件监听器同步加载区块 - 0841-Do-not-sync-load-chunk-for-dynamic-game-event-listen.patch
- [ ] 添加各种缺失的EntityDropItemEvent调用 - 0842-Add-various-missing-EntityDropItemEvent-calls.patch
- [ ] 修复服务器关闭时的插件记录器 - 0859-Fix-plugin-loggers-on-server-shutdown.patch
- [ ] 阻止大视角变化使服务器崩溃 - 0860-Stop-large-look-changes-from-crashing-the-server.patch
- [ ] 在更多地方触发EntityChangeBlockEvent - 0861-Fire-EntityChangeBlockEvent-in-more-places.patch
- [ ] 缺失的进食恢复原因 - 0862-Missing-eating-regain-reason.patch
- [ ] 缺失的效果原因 - 0863-Missing-effect-cause.patch
- [ ] 调用更多BlockPhysicsEvent - 0866-Call-BlockPhysicsEvent-more-often.patch
- [ ] 修复拼图方块踢出用户 - 0869-fix-Jigsaw-block-kicking-user.patch
- [ ] 使用BlockFormEvent将泥土转换为粘土 - 0870-use-BlockFormEvent-for-mud-converting-into-clay.patch
- [ ] 修复一堆原版bug - 0872-Fix-a-bunch-of-vanilla-bugs.patch
- [ ] 在导航期间移除不必要的onTrackingStart - 0873-Remove-unnecessary-onTrackingStart-during-navigation.patch
- [ ] 修复自定义猪灵喜爱物品 - 0874-Fix-custom-piglin-loved-items.patch
- [ ] EntityPickupItemEvent修复 - 0875-EntityPickupItemEvent-fixes.patch
- [ ] 正确处理冷却物品的交互 - 0876-Correctly-handle-interactions-with-items-on-cooldown.patch
- [ ] 检测无头JRE - 0881-Detect-headless-JREs.patch
- [ ] 修复实体载具碰撞事件未被调用 - 0882-fixed-entity-vehicle-collision-event-not-called.patch
- [ ] 优化泥土和雪传播 - 0883-optimized-dirt-and-snow-spreading.patch
- [ ] 添加EntityToggleSitEvent - 0884-Added-EntityToggleSitEvent.patch
- [ ] 忽略不可能的生成tick - 0887-Ignore-impossible-spawn-tick.patch
- [ ] 跟踪发射器烟花的投射物来源 - 0888-Track-projectile-source-for-fireworks-from-dispenser.patch
- [ ] 修复EntityArgument建议权限以与原版对齐 - 0889-Fix-EntityArgument-suggestion-permissions-to-align-w.patch
- [ ] 修复EntityCombustEvent取消无法完全阻止 - 0890-Fix-EntityCombustEvent-cancellation-cant-fully-preve.patch
- [ ] 防止指南针加载区块 - 0891-Prevent-compass-from-loading-chunks.patch
- [ ] 确保重置末影龙boss事件名称 - 0893-ensure-reset-EnderDragon-boss-event-name.patch
- [ ] 修复MC-252817绿色地图标记不消失 - 0894-fix-MC-252817-green-map-markers-do-not-disappear.patch
- [ ] 更多原版友好的更新交易方法 - 0896-More-vanilla-friendly-methods-to-update-trades.patch
- [ ] 在适当地方检查全局玩家列表 - 0898-check-global-player-list-where-appropriate.patch
- [ ] 修复由于真菌树导致的异步实体添加 - 0899-Fix-async-entity-add-due-to-fungus-trees.patch
- [ ] 修复玩家关闭时踢出 - 0903-Fix-player-kick-on-shutdown.patch
- [ ] 在菜单中同步副手槽 - 0904-Sync-offhand-slot-in-menus.patch
- [ ] 限制宠物视距 - 0906-Limit-pet-look-distance.patch
- [ ] 正确重新发送实体 - 0907-Properly-resend-entities.patch
- [ ] SpawnReason API的修复和添加 - 0908-Fixes-and-additions-to-the-SpawnReason-API.patch
- [ ] 修复乐器 - 0909-fix-Instruments.patch
- [ ] 改进一些热BlockBehavior和Fluid的内联 - 0910-Improve-inlining-for-some-hot-BlockBehavior-and-Flui.patch
- [ ] 修复关于堆栈的分发事件不一致 - 0911-Fix-inconsistencies-in-dispense-events-regarding-sta.patch
- [ ] 改进日志和错误 - 0914-Improve-logging-and-errors.patch
- [ ] 改进PortalEvents - 0915-Improve-PortalEvents.patch
- [ ] 添加缺失的SpigotConfig logCommands检查 - 0917-Add-missing-SpigotConfig-logCommands-check.patch
- [ ] 修复Allay在不跳舞时stopDancing的NPE - 0918-Fix-NPE-on-Allay-stopDancing-while-not-dancing.patch
- [ ] 飞行坠落伤害 - 0919-Flying-Fall-Damage.patch
- [ ] 使用单个玩家信息更新数据包加入 - 0923-Use-single-player-info-update-packet-on-join.patch
- [ ] 在EntityResurrectEvent期间正确缩小物品 - 0924-Correctly-shrink-items-during-EntityResurrectEvent.patch
- [ ] 移除CraftItemStack setAmount null赋值 - 0926-Remove-CraftItemStack-setAmount-null-assignment.patch
- [ ] 修复强制打开附魔台 - 0927-Fix-force-opening-enchantment-tables.patch
- [ ] 修复MC-157464防止睡觉村民移向 - 0929-Fix-MC-157464-Prevent-sleeping-villagers-moving-towa.patch
- [ ] 当捕获的方块状态过时时更新标志 - 0930-Update-the-flag-when-a-captured-block-state-is-outda.patch
- [ ] 修复HumanEntity drop不更新客户端inv - 0932-Fix-HumanEntity-drop-not-updating-the-client-inv.patch
- [ ] 正确处理ArmorStand隐身 - 0934-Correctly-handle-ArmorStand-invisibility.patch
- [ ] 修复实体伤害的进度触发器 - 0935-Fix-advancement-triggers-for-entity-damage.patch
- [ ] 修复生成时的文本显示错误 - 0936-Fix-text-display-error-on-spawn.patch
- [ ] 修复某些库存返回null位置 - 0937-Fix-certain-inventories-returning-null-Locations.patch
- [ ] 修复SpawnEggMeta get/setSpawnedType - 0939-Fix-SpawnEggMeta-get-setSpawnedType.patch
- [ ] 修复雕刻书架和唱片机setItem与空气 - 0940-Fix-chiseled-bookshelf-and-jukebox-setItem-with-air.patch
- [ ] 修复使用骨粉生成蜂巢 - 0942-Fix-beehives-generating-from-using-bonemeal.patch
- [ ] 修复熔炉类tile中坏配方的崩溃 - 0943-Fix-crash-relating-to-bad-recipes-in-furnace-like-ti.patch
- [ ] 像应该的那样处理序列违规 - 0944-Treat-sequence-violations-like-they-should-be.patch
- [ ] 移除记录的重复动画数据包 - 0945-remove-duplicate-animate-packet-for-records.patch
- [ ] 防止过期密钥影响新加入 - 0946-Prevent-causing-expired-keys-from-impacting-new-join.patch
- [ ] 防止从未加载区块触发GameEvents - 0947-Prevent-GameEvents-being-fired-from-unloaded-chunks.patch
- [ ] 为游戏规则存储使用数组 - 0948-Use-array-for-gamerule-storage.patch
- [ ] 修复一些上游床问题 - 0949-Fix-a-couple-of-upstream-bed-issues.patch
- [ ] 修复演示标志不启用演示模式 - 0950-Fix-demo-flag-not-enabling-demo-mode.patch
- [ ] 在陷阱门顶部早期破坏红石 - 0952-Break-redstone-on-top-of-trap-doors-early.patch
- [ ] 修复掉落方块的DamageCause - 0953-Fix-DamageCause-for-Falling-Blocks.patch
- [ ] 避免枚举字段的懒初始化 - 0954-Avoid-Lazy-Initialization-for-Enum-Fields.patch
- [ ] 更准确的isInOpenWater实现 - 0955-More-accurate-isInOpenWater-impl.patch
- [ ] 修复RegistryOps中lookups字段的并发访问 - 0956-Fix-concurrenct-access-to-lookups-field-in-RegistryO.patch
- [ ] 修复在没有附近玩家时破坏蜂巢 - 0958-Fix-destroying-beehive-without-any-players-nearby-th.patch
- [ ] 刷新投射物的ProjectileSource - 0960-Refresh-ProjectileSource-for-projectiles.patch
- [ ] 修复方块放置逻辑 - 0962-Fix-block-place-logic.patch
- [ ] 修复BlockItem ItemStacks的spigot声音播放 - 0963-Fix-spigot-sound-playing-for-BlockItem-ItemStacks.patch
- [ ] 忽略trim材料模式的内联定义 - 0964-Ignore-inline-definitions-of-trim-material-pattern.patch
- [ ] 为缺失方块调用BlockGrowEvent - 0965-Call-BlockGrowEvent-for-missing-blocks.patch
- [ ] 如果别名方块存在不强制icanhasbukkit默认 - 0966-Don-t-enforce-icanhasbukkit-default-if-alias-block-e.patch
- [ ] 修复缺失键选择器的MapLike垃圾邮件 - 0967-fix-MapLike-spam-for-missing-key-selector.patch
- [ ] 修复嗅探器removeExploredLocation - 0968-Fix-sniffer-removeExploredLocation.patch
- [ ] 临时预初始化PlayerChunkLoaderData以准备 - 0970-Temp-Pre-init-PlayerChunkLoaderData-in-order-to-prep.patch
- [ ] Folia调度器和拥有区域API - 0971-Folia-scheduler-and-owned-region-API.patch
- [ ] 修复烟花的不正确合成结果数量 - 0972-Fix-incorrect-crafting-result-amount-for-fireworks.patch
- [ ] 正确取消可用物品 - 0973-Properly-Cancel-Usable-Items.patch
- [ ] 只有玩家能看到时才tick物品框 - 0975-Only-tick-item-frames-if-players-can-see-it.patch
- [ ] 改进命令函数权限级别检查 - 0976-Improve-command-function-perm-level-checks.patch
- [ ] 调用缺失的BlockDispenseEvent - 0978-Call-missing-BlockDispenseEvent.patch
- [ ] 不为支撑方块检查加载区块 - 0979-Don-t-load-chunks-for-supporting-block-checks.patch
- [ ] 修复tadpole桶的物品meta - 0983-fix-item-meta-for-tadpole-buckets.patch
- [ ] 抑制物品Meta验证检查 - 0984-Suppress-Item-Meta-Validation-Checks.patch
- [ ] 修复BanList API - 0985-Fix-BanList-API.patch
- [ ] 确定熔岩和水流体爆炸阻力 - 0986-Determine-lava-and-water-fluid-explosion-resistance-.patch
- [ ] 修复绘画创建时可能的NPE - 0987-Fix-possible-NPE-on-painting-creation.patch
- [ ] 只为自然生成的流浪商人设置despawnTimer - 0988-Only-set-despawnTimer-for-Wandering-Traders-spawned-.patch
- [ ] ExperienceOrb应该调用EntitySpawnEvent - 0989-ExperienceOrb-should-call-EntitySpawnEvent.patch
- [ ] 不tick标志 - 0990-Don-t-tick-signs.patch
- [ ] 使紫水晶抛出传播和生长事件 - 0992-Make-Amethyst-throw-both-Spread-and-Grow-Events.patch
- [ ] 添加白名单事件 - 0993-Add-whitelist-events.patch
- [ ] 实现PlayerFailMoveEvent - 0994-Implement-PlayerFailMoveEvent.patch
- [ ] 只在非物品目标上擦除悦灵记忆 - 0995-Only-erase-allay-memory-on-non-item-targets.patch
- [ ] 修复生成显示实体时的旋转 - 0997-Fix-rotation-when-spawning-display-entities.patch
- [ ] 只捕获实际的树木生长 - 0998-Only-capture-actual-tree-growth.patch
- [ ] 为蘑菇方块传播事件使用正确来源 - 0999-Use-correct-source-for-mushroom-block-spread-event.patch
- [ ] 生成时在更多实体上尊重randomizeData - 1000-Respect-randomizeData-on-more-entities-when-spawning.patch
- [ ] 在api世界加载时使用正确种子 - 1001-Use-correct-seed-on-api-world-load.patch
- [ ] 移除范围外的UpgradeData邻居tick - 1002-Remove-UpgradeData-neighbour-ticks-outside-of-range.patch
- [ ] 在物品框上缓存地图ID - 1003-Cache-map-ids-on-item-frames.patch
- [ ] 修复自定义统计标准创建 - 1004-Fix-custom-statistic-criteria-creation.patch
- [ ] Effect的创可贴修复 - 1005-Bandaid-fix-for-Effect.patch
- [ ] 改进取消PreCreatureSpawnEvent与每个玩家 - 1008-Improve-cancelling-PreCreatureSpawnEvent-with-per-pl.patch
- [ ] 弃用并替换使用旧StructureType的方法 - 1009-Deprecate-and-replace-methods-with-old-StructureType.patch
- [ ] 如果发送命名空间被禁用不tab完成命名空间命令 - 1010-Don-t-tab-complete-namespaced-commands-if-send-names.patch
- [ ] 正确处理BlockBreakEvent isDropItems - 1011-Properly-handle-BlockBreakEvent-isDropItems.patch
- [ ] 为末影龙触发实体死亡事件 - 1012-Fire-entity-death-event-for-ender-dragon.patch
- [ ] 修复Boat getStatus的NPE - 1016-Fix-NPE-on-Boat-getStatus.patch
- [ ] 深度克隆未处理的nbt标签 - 1019-Deep-clone-unhandled-nbt-tags.patch
- [ ] 允许三叉戟自定义伤害 - 1022-Allow-trident-custom-damage.patch
- [ ] 实现OfflinePlayer isConnected - 1025-Implement-OfflinePlayer-isConnected.patch
- [ ] 修复库存不同步 - 1026-Fix-inventory-desync.patch
- [ ] 在载具中卡住时跳过POI查找 - 1030-Skip-POI-finding-if-stuck-in-vehicle.patch
- [ ] 在容器点击中添加槽位健全性检查 - 1031-Add-slot-sanity-checks-in-container-clicks.patch
- [ ] 为讲台调用BlockRedstoneEvents - 1032-Call-BlockRedstoneEvents-for-lecterns.patch
- [ ] 允许正确检查空物品堆栈 - 1033-Allow-proper-checking-of-empty-item-stacks.patch
- [ ] 修复mob的静默装备更改 - 1034-Fix-silent-equipment-change-for-mobs.patch
- [ ] 修复spigot的强制统计 - 1035-Fix-spigot-s-Forced-Stats.patch
- [ ] 添加缺失的InventoryHolders到库存 - 1036-Add-missing-InventoryHolders-to-inventories.patch

#### 🚀 剩余高级功能API
- [ ] 添加移动活塞API - 0886-Add-Moving-Piston-API.patch
- [ ] 添加PrePlayerAttackEntityEvent - 0892-Add-PrePlayerAttackEntityEvent.patch
- [ ] 添加Player监守者警告API - 0895-Add-Player-Warden-Warning-API.patch
- [ ] ItemStack伤害API - 0900-ItemStack-damage-API.patch
- [ ] 摩擦API - 0901-Friction-API.patch
- [ ] 控制玩家失眠和幻翼的能力 - 0902-Ability-to-control-player-s-insomnia-and-phantoms.patch
- [ ] Player实体跟踪事件 - 0905-Player-Entity-Tracking-Events.patch
- [ ] 添加BlockLockCheckEvent - 0912-Add-BlockLockCheckEvent.patch
- [ ] 为实体添加潜行API - 0913-Add-Sneaking-API-for-Entities.patch
- [ ] 添加爆炸方块状态到BlockExplodeEvent和EntityExplodeEvent - 0920-Add-exploded-block-state-to-BlockExplodeEvent-and-En.patch
- [ ] 暴露VehicleBlockCollisionEvent的预碰撞移动速度 - 0921-Expose-pre-collision-moving-velocity-to-VehicleBlock.patch
- [ ] 胜利屏幕API - 0925-Win-Screen-API.patch
- [ ] 添加实体身体偏航API - 0928-Add-Entity-Body-Yaw-API.patch
- [ ] 添加EntityFertilizeEggEvent - 0931-Add-EntityFertilizeEggEvent.patch
- [ ] 添加CompostItemEvent和EntityCompostItemEvent - 0933-Add-CompostItemEvent-and-EntityCompostItemEvent.patch
- [ ] 添加Shearable API - 0938-Add-Shearable-API.patch
- [ ] 添加Mob经验奖励API - 0951-Add-Mob-Experience-reward-API.patch
- [ ] 扩展PlayerItemMendEvent - 0959-Expand-PlayerItemMendEvent.patch
- [ ] 添加瞬态修饰符API - 0961-Add-transient-modifier-API.patch
- [ ] 添加移除所有活跃药水效果的方法 - 0969-Add-method-to-remove-all-active-potion-effects.patch
- [ ] 添加玩家编辑标志事件 - 0974-Add-event-for-player-editing-sign.patch
- [ ] 添加Sign getInteractableSideFor - 0981-Add-Sign-getInteractableSideFor.patch
- [ ] 更新客户端配方API - 0996-API-for-updating-recipes-on-clients.patch
- [ ] SculkCatalyst bloom API - 1006-SculkCatalyst-bloom-API.patch
- [ ] 实体记分板名称API - 1007-API-for-an-entity-s-scoreboard-name.patch
- [ ] 添加Player列表API - 1014-Add-Listing-API-for-Player.patch
- [ ] 在BlockDamageEvent期间暴露点击的BlockFace - 1015-Expose-clicked-BlockFace-during-BlockDamageEvent.patch
- [ ] 扩展Pose API - 1017-Expand-Pose-API.patch
- [ ] 更多DragonBattle API - 1018-More-DragonBattle-API.patch
- [ ] 添加PlayerPickItemEvent - 1020-Add-PlayerPickItemEvent.patch
- [ ] 在BlockCanBuildEvent期间暴露手部 - 1023-Expose-hand-during-BlockCanBuildEvent.patch
- [ ] 添加titleOverride到InventoryOpenEvent - 1027-Add-titleOverride-to-InventoryOpenEvent.patch
- [ ] 在实体查找前进行水晶传送门接近检查 - 1029-Do-crystal-portal-proximity-check-before-entity-look.patch

#### 🛠️ 剩余配置选项
- [ ] 可配置物品框地图光标更新间隔 - 0656-Configurable-item-frame-map-cursor-update-interval.patch
- [ ] 可配置怪物生成的最大方块光照 - 0750-Configurable-max-block-light-for-monster-spawning.patch
- [ ] 使水生动物生成高度可配置 - 0757-Make-water-animal-spawn-height-configurable.patch
- [ ] 添加受时间命令影响的世界配置选项 - 0759-Add-config-option-for-worlds-affected-by-time-cmd.patch
- [ ] 添加史莱姆生成的可配置高度 - 0763-Add-configurable-height-for-slime-spawn.patch
- [ ] 可配置区域压缩格式 - 0991-Configurable-Region-Compression-Format.patch
- [ ] 添加蜘蛛世界边界攀爬的配置选项 - 0916-Add-config-option-for-spider-worldborder-climbing.patch
- [ ] 添加禁用方块更新的选项 - 0977-Add-option-to-disable-block-updates.patch
- [ ] 配置聊天线程限制 - 0867-Configurable-chat-thread-limit.patch
- [ ] 添加火焰tick延迟选项 - 0885-Add-fire-tick-delay-option.patch
- [ ] 配置蜗牛蛋孵化时间 - 1028-Configure-sniffer-egg-hatch-time.patch
- [ ] 禁用实体标签的配置 - 0922-config-for-disabling-entity-tag-tags.patch
- [ ] 可配置实体Y坐标跟踪范围 - 1013-Configurable-entity-tracking-range-by-Y-coordinate.patch

---

## ✅ 已完成

### 🎉 1.0.8系列
- [x] 1.0.8-PRE1：支持Velocity Modern转发（Port Mohist and PCF）
- [x] 1.0.8-PRE2：并入MPEM的部分优化项
- [x] 1.0.8-PRE2：支持Adventure库
- [x] 1.0.8-PRE3：使用Paper方法优化初始化世界的速度
- [x] 1.0.8-RELEASE：更多i18n（打算用AI，我很懒）

---

## 📋 版本开发指南

### 🎯 优先级说明
- **高优先级**：核心功能，必须在该版本完成
- **中优先级**：重要功能，建议在该版本完成
- **低优先级**：可选功能，可推迟到后续版本

### 📅 开发时间线建议
- **1.1.0系列**: 约12-16周 (3-4个月)
  - PRE1-PRE5: 每个版本2-3周
  - RELEASE: 1-2周稳定性测试
- **1.2.0系列**: 约8-12周 (2-3个月)
  - PRE1-PRE3: 每个版本2-3周
  - RELEASE: 1-2周完善
- **1.3.0系列**: 约8-10周 (2-2.5个月)
  - 专注事件系统扩展
- **1.4.0系列**: 约8-10周 (2-2.5个月)
  - 专注实用工具和物品系统
- **1.5.0系列**: 约8-10周 (2-2.5个月)
  - 专注高级游戏机制
- **1.6.0系列**: 约6-8周 (1.5-2个月)
  - 专注大量Bug修复
- **1.7.0+**: 根据需求灵活安排剩余功能

### 🔄 迭代策略
1. **渐进式开发**: 从基础框架到高级功能
2. **功能聚焦**: 每个版本专注特定领域
3. **优先级驱动**: 核心功能优先，可选功能后置
4. **测试友好**: 每个版本功能数量适中，便于测试
5. **向后兼容**: 保持API稳定性和兼容性
6. **社区反馈**: 根据用户反馈调整优先级

### 📊 统计总结
- **总计功能数**: 780+ Paper patches
- **1.1.0系列**: 86项 (基础建设)
- **1.2.0系列**: 48项 (高级功能)
- **1.3.0系列**: 60项 (事件系统)
- **1.4.0系列**: 72项 (工具API)
- **1.5.0系列**: 60项 (游戏机制)
- **1.6.0系列**: 54项 (Bug修复)
- **1.7.0+系列**: 400+项 (剩余功能)

### 📝 注意事项
- 某些patch可能有依赖关系，需要按顺序实现
- 性能优化patch需要特别测试
- 事件系统变更需要考虑插件兼容性
- 网络相关变更需要测试多种连接场景
- Bug修复应该优先于新功能开发
- 保持与上游Paper的同步更新
